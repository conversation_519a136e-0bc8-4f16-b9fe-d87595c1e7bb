<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_applications', function (Blueprint $table) {
            $table->id();

            // Personal Information
            $table->string('name');
            $table->enum('gender', ['Male', 'Female']);
            $table->foreignId('nationality_id')->constrained('countries')->onDelete('cascade');
            $table->foreignId('residence_country_id')->constrained('countries')->onDelete('cascade');
            $table->date('birth_date');
            $table->string('email');
            $table->string('mobile_number');

            // Education Information
            $table->enum('educational_qualification', ['High School', 'Diploma', 'Bachelor', 'Master', 'PHD']);
            $table->foreignId('university_id')->nullable()->constrained()->onDelete('set null');
            $table->string('university_other')->nullable();
            $table->foreignId('university_country_id')->constrained('countries')->onDelete('cascade');
            $table->decimal('gpa', 4, 2);
            $table->enum('score_from', ['5', '4', '100']);
            $table->string('academic_major');
            $table->string('academic_major_other')->nullable();
            $table->year('graduation_year');

            // Professional Information
            $table->string('country_employer_last')->nullable();
            $table->integer('professional_certificates_count')->default(0);
            $table->json('professional_certificates')->nullable();
            $table->integer('memberships_count')->default(0);
            $table->json('memberships')->nullable();
            $table->string('total_experience');
            $table->string('last_position_title')->nullable();
            $table->string('last_employer_name')->nullable();
            $table->boolean('has_lubricants_experience')->default(false);

            // Languages and Additional Info
            $table->string('languages');
            $table->string('languages_other')->nullable();
            $table->string('how_heard_about_company');
            $table->string('career_fair_name')->nullable();
            $table->year('career_fair_year')->nullable();

            // File Upload
            $table->string('cv_file_path')->nullable();

            // Status
            $table->enum('status', ['pending', 'reviewed', 'shortlisted', 'rejected'])->default('pending');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_applications');
    }
};
