<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\CareersController;
use App\Http\Controllers\Admin\DashboardController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Careers routes
Route::get('/careers', [CareersController::class, 'index'])->name('careers.index');
Route::post('/careers', [CareersController::class, 'store'])->name('careers.store');
Route::get('/api/universities-by-country', [CareersController::class, 'getUniversitiesByCountry'])->name('api.universities-by-country');

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Admin routes (protected by auth middleware)
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/applications', [DashboardController::class, 'applications'])->name('applications.index');
    Route::get('/applications/{application}', [DashboardController::class, 'show'])->name('applications.show');
    Route::patch('/applications/{application}/status', [DashboardController::class, 'updateStatus'])->name('applications.update-status');
    Route::get('/applications/{application}/download-cv', [DashboardController::class, 'downloadCV'])->name('applications.download-cv');
});

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
