<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\University;
use App\Models\Country;

class UniversitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $saudiArabia = Country::where('code', 'SA')->first();
        $germany = Country::where('code', 'DE')->first();
        $switzerland = Country::where('code', 'CH')->first();

        $universities = [
            // Saudi Universities
            ['name' => 'King Saud University', 'country_id' => $saudiArabia->id],
            ['name' => 'Princess <PERSON> bin<PERSON>', 'country_id' => $saudiArabia->id],
            ['name' => 'Imam <PERSON> Islamic University', 'country_id' => $saudiArabia->id],
            ['name' => 'Prince Sultan University', 'country_id' => $saudiArabia->id],
            ['name' => 'College of Telecom & Information', 'country_id' => $saudiArabia->id],
            ['name' => 'Arab Open University', 'country_id' => $saudiArabia->id],
            ['name' => 'Riyadh College of Dentistry and Pharmacy', 'country_id' => $saudiArabia->id],
            ['name' => 'Al Yamamah University', 'country_id' => $saudiArabia->id],
            ['name' => 'Dar Al Uloom University', 'country_id' => $saudiArabia->id],
            ['name' => 'Alfaisal University', 'country_id' => $saudiArabia->id],
            ['name' => 'Arab East Colleges', 'country_id' => $saudiArabia->id],
            ['name' => 'AlMaarefa University', 'country_id' => $saudiArabia->id],
            ['name' => 'Prince Sattam Bin Abdulaziz University', 'country_id' => $saudiArabia->id],
            ['name' => 'Al Farabi College of Dentistry and Nursing', 'country_id' => $saudiArabia->id],
            ['name' => 'Technical Trainers College', 'country_id' => $saudiArabia->id],
            ['name' => 'Majmaah University', 'country_id' => $saudiArabia->id],
            ['name' => 'Shaqra University', 'country_id' => $saudiArabia->id],
            ['name' => 'Saudi Electronic University', 'country_id' => $saudiArabia->id],
            ['name' => 'King Abdulaziz University', 'country_id' => $saudiArabia->id],
            ['name' => 'Umm Al-Qura University', 'country_id' => $saudiArabia->id],
            ['name' => 'Leadership Community College', 'country_id' => $saudiArabia->id],
            ['name' => 'Jeddah College of Technology', 'country_id' => $saudiArabia->id],
            ['name' => 'Effat University', 'country_id' => $saudiArabia->id],
            ['name' => 'Dar Al-Hekma College', 'country_id' => $saudiArabia->id],
            ['name' => 'Fakeeh College for Medical Sciences', 'country_id' => $saudiArabia->id],
            ['name' => 'University of Business and Technology', 'country_id' => $saudiArabia->id],
            ['name' => 'Prince Sultan Aviation Academy', 'country_id' => $saudiArabia->id],
            ['name' => 'Taif University', 'country_id' => $saudiArabia->id],
            ['name' => 'Taif College of Technology', 'country_id' => $saudiArabia->id],
            ['name' => 'Batterjee Medical College', 'country_id' => $saudiArabia->id],
            ['name' => 'Prince Sultan College For Tourism and Business', 'country_id' => $saudiArabia->id],
            ['name' => 'King Abdullah University of Science and Technology', 'country_id' => $saudiArabia->id],
            ['name' => 'Jeddah Teacher\'s College', 'country_id' => $saudiArabia->id],
            ['name' => 'College of Telecom & Electronics', 'country_id' => $saudiArabia->id],
            ['name' => 'Jeddah International College', 'country_id' => $saudiArabia->id],
            ['name' => 'Jeddah College of Health Care', 'country_id' => $saudiArabia->id],
            ['name' => 'Ibn Sina National College for Medical Studies', 'country_id' => $saudiArabia->id],
            ['name' => 'University of Jeddah', 'country_id' => $saudiArabia->id],
            ['name' => 'Prince Mohammad bin Salman College of Business and Entrepreneurship', 'country_id' => $saudiArabia->id],
            ['name' => 'Islamic University of Medina', 'country_id' => $saudiArabia->id],
            ['name' => 'Yanbu Industrial College', 'country_id' => $saudiArabia->id],
            ['name' => 'Al-Madinah College of Technology', 'country_id' => $saudiArabia->id],
            ['name' => 'Taibah University', 'country_id' => $saudiArabia->id],
            ['name' => 'Yanbu University College', 'country_id' => $saudiArabia->id],
            ['name' => 'Madinah Institute for Leadership and Entrepreneurship (MILE)', 'country_id' => $saudiArabia->id],
            ['name' => 'Prince Mugrin University', 'country_id' => $saudiArabia->id],
            ['name' => 'Mohammed Almana College of Medical Sciences', 'country_id' => $saudiArabia->id],
            ['name' => 'Dammam College of Technology', 'country_id' => $saudiArabia->id],
            ['name' => 'Dammam Community College', 'country_id' => $saudiArabia->id],
            ['name' => 'Al Ahsa College of Technology', 'country_id' => $saudiArabia->id],
            ['name' => 'Imam Abdulrahman Bin Faisal University', 'country_id' => $saudiArabia->id],
            ['name' => 'Prince Mohammad Bin Fahd University', 'country_id' => $saudiArabia->id],
            ['name' => 'Saad College of Nursing and Allied Health Sciences', 'country_id' => $saudiArabia->id],
            ['name' => 'Al Baha University', 'country_id' => $saudiArabia->id],
            ['name' => 'Al Jouf University', 'country_id' => $saudiArabia->id],
            ['name' => 'Qassim University', 'country_id' => $saudiArabia->id],
            ['name' => 'Najran University', 'country_id' => $saudiArabia->id],
            ['name' => 'Northern Borders University', 'country_id' => $saudiArabia->id],
            ['name' => 'Buraydah Colleges', 'country_id' => $saudiArabia->id],
            ['name' => 'Gulf Colleges', 'country_id' => $saudiArabia->id],
            ['name' => 'Inaya Medical Colleges', 'country_id' => $saudiArabia->id],
            ['name' => 'Mustaqbal University', 'country_id' => $saudiArabia->id],
            ['name' => 'Onaizah Colleges', 'country_id' => $saudiArabia->id],
            ['name' => 'Al Ghad International Colleges', 'country_id' => $saudiArabia->id],
            ['name' => 'Al Rayyan Colleges', 'country_id' => $saudiArabia->id],
            ['name' => 'Al-Baha Private College of Science', 'country_id' => $saudiArabia->id],
            ['name' => 'AlFayha College', 'country_id' => $saudiArabia->id],
            ['name' => 'Al-Moosa College of Health Sciences', 'country_id' => $saudiArabia->id],
            ['name' => 'Al-Riyada College for Health Science', 'country_id' => $saudiArabia->id],
            ['name' => 'Ibn Rushd College of Management Sciences', 'country_id' => $saudiArabia->id],
            ['name' => 'Riyadh Elm University', 'country_id' => $saudiArabia->id],

            // International Universities (Sample)
            ['name' => 'University of Geneva', 'country_id' => $switzerland->id],
            ['name' => 'University of Bern', 'country_id' => $switzerland->id],
            ['name' => 'University of Basel', 'country_id' => $switzerland->id],
            ['name' => 'University of Lausanne', 'country_id' => $switzerland->id],
            ['name' => 'University of Freiburg', 'country_id' => $switzerland->id],
            ['name' => 'University of Hamburg', 'country_id' => $germany->id],
            ['name' => 'University of Cologne', 'country_id' => $germany->id],
            ['name' => 'University of Bonn', 'country_id' => $germany->id],
            ['name' => 'University of Münster', 'country_id' => $germany->id],
            ['name' => 'University of Göttingen', 'country_id' => $germany->id],
            ['name' => 'University of Heidelberg', 'country_id' => $germany->id],
            ['name' => 'University of Tübingen', 'country_id' => $germany->id],
            ['name' => 'University of Stuttgart', 'country_id' => $germany->id],
            ['name' => 'University of Erlangen-Nuremberg', 'country_id' => $germany->id],
            ['name' => 'University of Würzburg', 'country_id' => $germany->id],
            ['name' => 'University of Regensburg', 'country_id' => $germany->id],
            ['name' => 'University of Kiel', 'country_id' => $germany->id],
            ['name' => 'University of Greifswald', 'country_id' => $germany->id],
            ['name' => 'University of Rostock', 'country_id' => $germany->id],
            ['name' => 'University of Leipzig', 'country_id' => $germany->id],
            ['name' => 'University of Dresden', 'country_id' => $germany->id],
            ['name' => 'University of Jena', 'country_id' => $germany->id],
            ['name' => 'University of Halle-Wittenberg', 'country_id' => $germany->id],
            ['name' => 'University of Magdeburg', 'country_id' => $germany->id],
            ['name' => 'University of Potsdam', 'country_id' => $germany->id],
            ['name' => 'University of Bremen', 'country_id' => $germany->id],
            ['name' => 'University of Oldenburg', 'country_id' => $germany->id],
            ['name' => 'University of Hannover', 'country_id' => $germany->id],
            ['name' => 'University of Braunschweig', 'country_id' => $germany->id],
            ['name' => 'University of Kaiserslautern', 'country_id' => $germany->id],
        ];

        foreach ($universities as $university) {
            University::create($university);
        }
    }
}
