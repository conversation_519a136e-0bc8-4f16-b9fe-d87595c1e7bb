<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\JobApplication;
use Illuminate\Support\Facades\Storage;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $totalApplications = JobApplication::count();
        $pendingApplications = JobApplication::where('status', 'pending')->count();
        $reviewedApplications = JobApplication::where('status', 'reviewed')->count();
        $shortlistedApplications = JobApplication::where('status', 'shortlisted')->count();

        return view('admin.dashboard', compact(
            'totalApplications',
            'pendingApplications',
            'reviewedApplications',
            'shortlistedApplications'
        ));
    }

    public function applications(Request $request)
    {
        if ($request->ajax()) {
            $applications = JobApplication::with(['nationality', 'residenceCountry', 'university'])
                ->select([
                    'id',
                    'name',
                    'email',
                    'mobile_number',
                    'nationality_id',
                    'educational_qualification',
                    'university_id',
                    'total_experience',
                    'status',
                    'created_at'
                ]);

            return datatables($applications)
                ->addColumn('nationality', function ($application) {
                    return $application->nationality->name ?? 'N/A';
                })
                ->addColumn('university', function ($application) {
                    return $application->university->name ?? $application->university_other ?? 'N/A';
                })
                ->addColumn('actions', function ($application) {
                    return '
                        <div class="flex space-x-2">
                            <a href="' . route('admin.applications.show', $application->id) . '"
                               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs">
                                View
                            </a>
                            <select onchange="updateStatus(' . $application->id . ', this.value)"
                                    class="text-xs border rounded px-1 py-1">
                                <option value="pending"' . ($application->status === 'pending' ? ' selected' : '') . '>Pending</option>
                                <option value="reviewed"' . ($application->status === 'reviewed' ? ' selected' : '') . '>Reviewed</option>
                                <option value="shortlisted"' . ($application->status === 'shortlisted' ? ' selected' : '') . '>Shortlisted</option>
                                <option value="rejected"' . ($application->status === 'rejected' ? ' selected' : '') . '>Rejected</option>
                            </select>
                        </div>
                    ';
                })
                ->addColumn('status_badge', function ($application) {
                    $colors = [
                        'pending' => 'bg-yellow-100 text-yellow-800',
                        'reviewed' => 'bg-blue-100 text-blue-800',
                        'shortlisted' => 'bg-green-100 text-green-800',
                        'rejected' => 'bg-red-100 text-red-800',
                    ];

                    return '<span class="px-2 py-1 text-xs font-semibold rounded-full ' .
                           ($colors[$application->status] ?? 'bg-gray-100 text-gray-800') . '">' .
                           ucfirst($application->status) . '</span>';
                })
                ->rawColumns(['actions', 'status_badge'])
                ->make(true);
        }

        return view('admin.applications.index');
    }

    public function show(JobApplication $application)
    {
        $application->load(['nationality', 'residenceCountry', 'universityCountry', 'university']);

        return view('admin.applications.show', compact('application'));
    }

    public function updateStatus(Request $request, JobApplication $application)
    {
        $request->validate([
            'status' => 'required|in:pending,reviewed,shortlisted,rejected'
        ]);

        $application->update(['status' => $request->status]);

        return response()->json(['success' => true]);
    }

    public function downloadCV(JobApplication $application)
    {
        if (!$application->cv_file_path || !Storage::disk('public')->exists($application->cv_file_path)) {
            abort(404, 'CV file not found');
        }

        return Storage::disk('public')->download($application->cv_file_path);
    }
}
