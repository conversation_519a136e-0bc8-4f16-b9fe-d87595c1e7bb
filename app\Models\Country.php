<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Country extends Model
{
    protected $fillable = [
        'name',
        'code',
    ];

    public function universities(): HasMany
    {
        return $this->hasMany(University::class);
    }

    public function jobApplicationsAsNationality(): Has<PERSON><PERSON>
    {
        return $this->hasMany(JobApplication::class, 'nationality_id');
    }

    public function jobApplicationsAsResidence(): Has<PERSON><PERSON>
    {
        return $this->hasMany(JobApplication::class, 'residence_country_id');
    }

    public function jobApplicationsAsUniversityCountry(): HasMany
    {
        return $this->hasMany(JobApplication::class, 'university_country_id');
    }
}
