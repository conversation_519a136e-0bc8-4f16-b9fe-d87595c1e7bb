<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Careers - Job Application</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen py-12">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-4">Job Application</h1>
                <p class="text-lg text-gray-600">Welcome to TotalEnergies job application form. Please complete all of following fields accurately and clearly. The information you provide will be used solely for recruitment purpose</p>
            </div>

            <!-- Success Message -->
            <?php if(session('success')): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <!-- Form -->
            <div class="bg-white shadow-lg rounded-lg p-8">
                <form action="<?php echo e(route('careers.store')); ?>" method="POST" enctype="multipart/form-data" x-data="careerForm()">
                    <?php echo csrf_field(); ?>

                    <!-- Personal Information Section -->
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold text-gray-800 mb-6 border-b pb-2">Personal Information</h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name *</label>
                                <input type="text" id="name" name="name" value="<?php echo e(old('name')); ?>" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Gender -->
                            <div>
                                <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">Gender *</label>
                                <select id="gender" name="gender" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">---- Select ----</option>
                                    <option value="Male" <?php echo e(old('gender') === 'Male' ? 'selected' : ''); ?>>Male</option>
                                    <option value="Female" <?php echo e(old('gender') === 'Female' ? 'selected' : ''); ?>>Female</option>
                                </select>
                                <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Nationality -->
                            <div>
                                <label for="nationality_id" class="block text-sm font-medium text-gray-700 mb-2">Nationality *</label>
                                <select id="nationality_id" name="nationality_id" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">---- Select ----</option>
                                    <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($country->id); ?>" <?php echo e(old('nationality_id') == $country->id ? 'selected' : ''); ?>>
                                            <?php echo e($country->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['nationality_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Residence Country -->
                            <div>
                                <label for="residence_country_id" class="block text-sm font-medium text-gray-700 mb-2">Residence Country *</label>
                                <select id="residence_country_id" name="residence_country_id" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">---- Select ----</option>
                                    <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($country->id); ?>" <?php echo e(old('residence_country_id') == $country->id ? 'selected' : ''); ?>>
                                            <?php echo e($country->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['residence_country_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Birth Date -->
                            <div>
                                <label for="birth_date" class="block text-sm font-medium text-gray-700 mb-2">Birth Date *</label>
                                <input type="date" id="birth_date" name="birth_date" value="<?php echo e(old('birth_date')); ?>" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php $__errorArgs = ['birth_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Email -->
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                <input type="email" id="email" name="email" value="<?php echo e(old('email')); ?>" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Mobile Number -->
                            <div>
                                <label for="mobile_number" class="block text-sm font-medium text-gray-700 mb-2">Mobile Number *</label>
                                <input type="tel" id="mobile_number" name="mobile_number" value="<?php echo e(old('mobile_number')); ?>" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php $__errorArgs = ['mobile_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Education Information Section -->
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold text-gray-800 mb-6 border-b pb-2">Education Information</h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Educational Qualification -->
                            <div>
                                <label for="educational_qualification" class="block text-sm font-medium text-gray-700 mb-2">Educational Qualification *</label>
                                <select id="educational_qualification" name="educational_qualification" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">---- Select ----</option>
                                    <option value="High School" <?php echo e(old('educational_qualification') === 'High School' ? 'selected' : ''); ?>>High School</option>
                                    <option value="Diploma" <?php echo e(old('educational_qualification') === 'Diploma' ? 'selected' : ''); ?>>Diploma</option>
                                    <option value="Bachelor" <?php echo e(old('educational_qualification') === 'Bachelor' ? 'selected' : ''); ?>>Bachelor</option>
                                    <option value="Master" <?php echo e(old('educational_qualification') === 'Master' ? 'selected' : ''); ?>>Master</option>
                                    <option value="PHD" <?php echo e(old('educational_qualification') === 'PHD' ? 'selected' : ''); ?>>PHD</option>
                                </select>
                                <?php $__errorArgs = ['educational_qualification'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- University Country -->
                            <div>
                                <label for="university_country_id" class="block text-sm font-medium text-gray-700 mb-2">Country of the University *</label>
                                <select id="university_country_id" name="university_country_id" required x-model="selectedUniversityCountry" @change="loadUniversities()"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">---- Select ----</option>
                                    <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($country->id); ?>" <?php echo e(old('university_country_id') == $country->id ? 'selected' : ''); ?>>
                                            <?php echo e($country->name); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['university_country_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- University Name -->
                            <div>
                                <label for="university_id" class="block text-sm font-medium text-gray-700 mb-2">University Name *</label>
                                <select id="university_id" name="university_id" x-model="selectedUniversity" @change="checkOtherUniversity()"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">---- Select ----</option>
                                    <template x-for="university in filteredUniversities" :key="university.id">
                                        <option :value="university.id" x-text="university.name"></option>
                                    </template>
                                    <option value="other">Other</option>
                                </select>
                                <?php $__errorArgs = ['university_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- University Other -->
                            <div x-show="showOtherUniversity">
                                <label for="university_other" class="block text-sm font-medium text-gray-700 mb-2">University Name (Other) *</label>
                                <input type="text" id="university_other" name="university_other" value="<?php echo e(old('university_other')); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php $__errorArgs = ['university_other'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                            <!-- GPA -->
                            <div>
                                <label for="gpa" class="block text-sm font-medium text-gray-700 mb-2">GPA *</label>
                                <input type="number" step="0.01" id="gpa" name="gpa" value="<?php echo e(old('gpa')); ?>" required min="0" max="100"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php $__errorArgs = ['gpa'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Score From -->
                            <div>
                                <label for="score_from" class="block text-sm font-medium text-gray-700 mb-2">Score from *</label>
                                <select id="score_from" name="score_from" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">---- Select ----</option>
                                    <option value="5" <?php echo e(old('score_from') === '5' ? 'selected' : ''); ?>>5</option>
                                    <option value="4" <?php echo e(old('score_from') === '4' ? 'selected' : ''); ?>>4</option>
                                    <option value="100" <?php echo e(old('score_from') === '100' ? 'selected' : ''); ?>>100</option>
                                </select>
                                <?php $__errorArgs = ['score_from'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Academic Major -->
                            <div>
                                <label for="academic_major" class="block text-sm font-medium text-gray-700 mb-2">Academic Major *</label>
                                <select id="academic_major" name="academic_major" required x-model="selectedMajor" @change="checkOtherMajor()"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">---- Select ----</option>
                                    <option value="Accounting">Accounting</option>
                                    <option value="Business Administration">Business Administration</option>
                                    <option value="Engineering">Engineering</option>
                                    <option value="Computer Science">Computer Science</option>
                                    <option value="Finance">Finance</option>
                                    <option value="Marketing">Marketing</option>
                                    <option value="Other">Other</option>
                                </select>
                                <?php $__errorArgs = ['academic_major'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Academic Major Other -->
                            <div x-show="showOtherMajor">
                                <label for="academic_major_other" class="block text-sm font-medium text-gray-700 mb-2">Academic Major (Other) *</label>
                                <input type="text" id="academic_major_other" name="academic_major_other" value="<?php echo e(old('academic_major_other')); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php $__errorArgs = ['academic_major_other'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Graduation Year -->
                            <div>
                                <label for="graduation_year" class="block text-sm font-medium text-gray-700 mb-2">Graduation Year *</label>
                                <input type="number" id="graduation_year" name="graduation_year" value="<?php echo e(old('graduation_year')); ?>" required min="1950" max="<?php echo e(date('Y') + 10); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php $__errorArgs = ['graduation_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Professional Information Section -->
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold text-gray-800 mb-6 border-b pb-2">Professional Information</h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Total Experience -->
                            <div>
                                <label for="total_experience" class="block text-sm font-medium text-gray-700 mb-2">Total Years of Experience *</label>
                                <select id="total_experience" name="total_experience" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">---- Select ----</option>
                                    <option value="Fresh Graduate">Fresh Graduate</option>
                                    <option value="< 1 year">< 1 year</option>
                                    <option value="1-2 years">1-2 years</option>
                                    <option value="2-3 years">2-3 years</option>
                                    <option value="3-5 years">3-5 years</option>
                                    <option value="5-7 years">5-7 years</option>
                                    <option value="7-10 years">7-10 years</option>
                                    <option value="10-15 years">10-15 years</option>
                                    <option value="> 15 years">> 15 years</option>
                                </select>
                                <?php $__errorArgs = ['total_experience'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Last Position Title -->
                            <div>
                                <label for="last_position_title" class="block text-sm font-medium text-gray-700 mb-2">Last Position Title</label>
                                <input type="text" id="last_position_title" name="last_position_title" value="<?php echo e(old('last_position_title')); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php $__errorArgs = ['last_position_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Last Employer Name -->
                            <div>
                                <label for="last_employer_name" class="block text-sm font-medium text-gray-700 mb-2">Last Employer Name</label>
                                <input type="text" id="last_employer_name" name="last_employer_name" value="<?php echo e(old('last_employer_name')); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php $__errorArgs = ['last_employer_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Lubricants Experience -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Do you have experience in Lubricants Business? *</label>
                                <div class="flex space-x-4">
                                    <label class="flex items-center">
                                        <input type="radio" name="has_lubricants_experience" value="1" <?php echo e(old('has_lubricants_experience') == '1' ? 'checked' : ''); ?> required
                                               class="mr-2 text-blue-600 focus:ring-blue-500">
                                        Yes
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="has_lubricants_experience" value="0" <?php echo e(old('has_lubricants_experience') == '0' ? 'checked' : ''); ?> required
                                               class="mr-2 text-blue-600 focus:ring-blue-500">
                                        No
                                    </label>
                                </div>
                                <?php $__errorArgs = ['has_lubricants_experience'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Professional Certificates Count -->
                            <div>
                                <label for="professional_certificates_count" class="block text-sm font-medium text-gray-700 mb-2">Number of Professional Certificates *</label>
                                <select id="professional_certificates_count" name="professional_certificates_count" required x-model="certificatesCount" @change="updateCertificatesFields()"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="0">0</option>
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">More</option>
                                </select>
                                <?php $__errorArgs = ['professional_certificates_count'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Languages -->
                            <div>
                                <label for="languages" class="block text-sm font-medium text-gray-700 mb-2">Languages *</label>
                                <select id="languages" name="languages" required x-model="selectedLanguage" @change="checkOtherLanguage()"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">---- Select ----</option>
                                    <option value="Arabic">Arabic</option>
                                    <option value="English">English</option>
                                    <option value="French">French</option>
                                    <option value="Other">Other</option>
                                </select>
                                <?php $__errorArgs = ['languages'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Languages Other -->
                            <div x-show="showOtherLanguage">
                                <label for="languages_other" class="block text-sm font-medium text-gray-700 mb-2">Languages (Other) *</label>
                                <input type="text" id="languages_other" name="languages_other" value="<?php echo e(old('languages_other')); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php $__errorArgs = ['languages_other'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- How heard about company -->
                            <div>
                                <label for="how_heard_about_company" class="block text-sm font-medium text-gray-700 mb-2">How did you hear about TotalEnergies? *</label>
                                <select id="how_heard_about_company" name="how_heard_about_company" required x-model="heardAbout" @change="checkCareerFair()"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                    <option value="">---- Select ----</option>
                                    <option value="Career Fair">Career Fair</option>
                                    <option value="Networking Event">Networking Event</option>
                                    <option value="Job Board">Job Board (e.g., LinkedIn, Indeed, Glassdoor)</option>
                                    <option value="Company Website">Company Website</option>
                                    <option value="Employee Referral">Employee Referral</option>
                                    <option value="Social Media">Social Media (e.g., Facebook, Twitter, Instagram)</option>
                                    <option value="Recruitment Agency">Recruitment Agency</option>
                                    <option value="Walk-in">Walk-in / Direct Application (Walking CV)</option>
                                </select>
                                <?php $__errorArgs = ['how_heard_about_company'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Career Fair Name -->
                            <div x-show="showCareerFair">
                                <label for="career_fair_name" class="block text-sm font-medium text-gray-700 mb-2">Career Fair Name</label>
                                <input type="text" id="career_fair_name" name="career_fair_name" value="<?php echo e(old('career_fair_name')); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php $__errorArgs = ['career_fair_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Career Fair Year -->
                            <div x-show="showCareerFair">
                                <label for="career_fair_year" class="block text-sm font-medium text-gray-700 mb-2">Career Fair Year *</label>
                                <input type="number" id="career_fair_year" name="career_fair_year" value="<?php echo e(old('career_fair_year')); ?>" min="2000" max="<?php echo e(date('Y') + 5); ?>"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <?php $__errorArgs = ['career_fair_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- File Upload Section -->
                    <div class="mb-8">
                        <h2 class="text-xl font-semibold text-gray-800 mb-6 border-b pb-2">File Upload</h2>

                        <div>
                            <label for="cv_file" class="block text-sm font-medium text-gray-700 mb-2">CV File Upload *</label>
                            <input type="file" id="cv_file" name="cv_file" accept=".pdf,.doc,.docx" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <p class="text-xs text-gray-500 mt-1">Click or drag a file to this area to upload. (PDF, DOC, DOCX - Max 5MB)</p>
                            <?php $__errorArgs = ['cv_file'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="text-red-500 text-xs mt-1"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg transition duration-200">
                            Submit Application
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function careerForm() {
            return {
                selectedUniversityCountry: '<?php echo e(old('university_country_id')); ?>',
                selectedUniversity: '<?php echo e(old('university_id')); ?>',
                filteredUniversities: <?php echo json_encode($universities, 15, 512) ?>,
                showOtherUniversity: <?php echo e(old('university_id') === 'other' ? 'true' : 'false'); ?>,
                selectedMajor: '<?php echo e(old('academic_major')); ?>',
                showOtherMajor: <?php echo e(old('academic_major') === 'Other' ? 'true' : 'false'); ?>,
                selectedLanguage: '<?php echo e(old('languages')); ?>',
                showOtherLanguage: <?php echo e(old('languages') === 'Other' ? 'true' : 'false'); ?>,
                heardAbout: '<?php echo e(old('how_heard_about_company')); ?>',
                showCareerFair: <?php echo e(old('how_heard_about_company') === 'Career Fair' ? 'true' : 'false'); ?>,
                certificatesCount: '<?php echo e(old('professional_certificates_count', '0')); ?>',

                init() {
                    this.loadUniversities();
                    this.checkOtherUniversity();
                    this.checkOtherMajor();
                    this.checkOtherLanguage();
                    this.checkCareerFair();
                },

                loadUniversities() {
                    if (this.selectedUniversityCountry) {
                        fetch(`/api/universities-by-country?country_id=${this.selectedUniversityCountry}`)
                            .then(response => response.json())
                            .then(data => {
                                this.filteredUniversities = data;
                            });
                    } else {
                        this.filteredUniversities = [];
                    }
                    this.selectedUniversity = '';
                    this.showOtherUniversity = false;
                },

                checkOtherUniversity() {
                    this.showOtherUniversity = this.selectedUniversity === 'other';
                },

                checkOtherMajor() {
                    this.showOtherMajor = this.selectedMajor === 'Other';
                },

                checkOtherLanguage() {
                    this.showOtherLanguage = this.selectedLanguage === 'Other';
                },

                checkCareerFair() {
                    this.showCareerFair = this.heardAbout === 'Career Fair';
                },

                updateCertificatesFields() {
                    // This can be extended to show dynamic certificate input fields
                    console.log('Certificates count:', this.certificatesCount);
                }
            }
        }
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\TTE\resources\views/careers/index.blade.php ENDPATH**/ ?>