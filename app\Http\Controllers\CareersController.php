<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\JobApplication;
use App\Models\Country;
use App\Models\University;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class CareersController extends Controller
{
    public function index()
    {
        $countries = Country::orderBy('name')->get();
        $universities = University::with('country')->orderBy('name')->get();

        return view('careers.index', compact('countries', 'universities'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'gender' => 'required|in:Male,Female',
            'nationality_id' => 'required|exists:countries,id',
            'residence_country_id' => 'required|exists:countries,id',
            'birth_date' => 'required|date|before:today',
            'email' => 'required|email|max:255',
            'mobile_number' => 'required|string|max:20',
            'educational_qualification' => 'required|in:High School,Diploma,Bachelor,Master,PHD',
            'university_id' => 'nullable|exists:universities,id',
            'university_other' => 'nullable|string|max:255',
            'university_country_id' => 'required|exists:countries,id',
            'gpa' => 'required|numeric|min:0|max:100',
            'score_from' => 'required|in:5,4,100',
            'academic_major' => 'required|string|max:255',
            'academic_major_other' => 'nullable|string|max:255',
            'graduation_year' => 'required|integer|min:1950|max:' . (date('Y') + 10),
            'country_employer_last' => 'nullable|string|max:255',
            'professional_certificates_count' => 'required|integer|min:0|max:50',
            'professional_certificates' => 'nullable|array',
            'professional_certificates.*' => 'string|max:255',
            'memberships_count' => 'required|integer|min:0|max:50',
            'memberships' => 'nullable|array',
            'memberships.*' => 'string|max:255',
            'total_experience' => 'required|string|max:255',
            'last_position_title' => 'nullable|string|max:255',
            'last_employer_name' => 'nullable|string|max:255',
            'has_lubricants_experience' => 'required|boolean',
            'languages' => 'required|string|max:255',
            'languages_other' => 'nullable|string|max:255',
            'how_heard_about_company' => 'required|string|max:255',
            'career_fair_name' => 'nullable|string|max:255',
            'career_fair_year' => 'nullable|integer|min:2000|max:' . (date('Y') + 5),
            'cv_file' => 'required|file|mimes:pdf,doc,docx|max:5120', // 5MB max
        ]);

        // Handle file upload
        if ($request->hasFile('cv_file')) {
            $file = $request->file('cv_file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('cv_files', $filename, 'public');
            $validated['cv_file_path'] = $path;
        }

        // Remove the cv_file from validated data as we've handled it separately
        unset($validated['cv_file']);

        JobApplication::create($validated);

        return redirect()->route('careers.index')->with('success', 'Your application has been submitted successfully!');
    }

    public function getUniversitiesByCountry(Request $request)
    {
        $countryId = $request->get('country_id');
        $universities = University::where('country_id', $countryId)->orderBy('name')->get();

        return response()->json($universities);
    }
}
