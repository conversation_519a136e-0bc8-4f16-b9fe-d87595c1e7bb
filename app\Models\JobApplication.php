<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Casts\Attribute;

class JobApplication extends Model
{
    protected $fillable = [
        'name',
        'gender',
        'nationality_id',
        'residence_country_id',
        'birth_date',
        'email',
        'mobile_number',
        'educational_qualification',
        'university_id',
        'university_other',
        'university_country_id',
        'gpa',
        'score_from',
        'academic_major',
        'academic_major_other',
        'graduation_year',
        'country_employer_last',
        'professional_certificates_count',
        'professional_certificates',
        'memberships_count',
        'memberships',
        'total_experience',
        'last_position_title',
        'last_employer_name',
        'has_lubricants_experience',
        'languages',
        'languages_other',
        'how_heard_about_company',
        'career_fair_name',
        'career_fair_year',
        'cv_file_path',
        'status',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'has_lubricants_experience' => 'boolean',
        'professional_certificates' => 'array',
        'memberships' => 'array',
        'graduation_year' => 'integer',
        'career_fair_year' => 'integer',
        'gpa' => 'decimal:2',
    ];

    public function nationality(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'nationality_id');
    }

    public function residenceCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'residence_country_id');
    }

    public function universityCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'university_country_id');
    }

    public function university(): BelongsTo
    {
        return $this->belongsTo(University::class);
    }

    protected function age(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->birth_date ? $this->birth_date->age : null,
        );
    }
}
