<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                Application Details - {{ $application->name }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('admin.applications.index') }}" 
                   class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to List
                </a>
                @if($application->cv_file_path)
                    <a href="{{ route('admin.applications.download-cv', $application) }}" 
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Download CV
                    </a>
                @endif
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Status Update -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Application Status</h3>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm font-medium text-gray-700">Current Status:</span>
                        <span class="px-3 py-1 text-sm font-semibold rounded-full 
                            @if($application->status === 'pending') bg-yellow-100 text-yellow-800
                            @elseif($application->status === 'reviewed') bg-blue-100 text-blue-800
                            @elseif($application->status === 'shortlisted') bg-green-100 text-green-800
                            @elseif($application->status === 'rejected') bg-red-100 text-red-800
                            @endif">
                            {{ ucfirst($application->status) }}
                        </span>
                        <select onchange="updateStatus({{ $application->id }}, this.value)" 
                                class="border border-gray-300 rounded-md px-3 py-1 text-sm">
                            <option value="pending" {{ $application->status === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="reviewed" {{ $application->status === 'reviewed' ? 'selected' : '' }}>Reviewed</option>
                            <option value="shortlisted" {{ $application->status === 'shortlisted' ? 'selected' : '' }}>Shortlisted</option>
                            <option value="rejected" {{ $application->status === 'rejected' ? 'selected' : '' }}>Rejected</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Personal Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4 border-b pb-2">Personal Information</h3>
                        <dl class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Name</dt>
                                <dd class="text-sm text-gray-900">{{ $application->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Gender</dt>
                                <dd class="text-sm text-gray-900">{{ $application->gender }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Nationality</dt>
                                <dd class="text-sm text-gray-900">{{ $application->nationality->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Residence Country</dt>
                                <dd class="text-sm text-gray-900">{{ $application->residenceCountry->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Birth Date</dt>
                                <dd class="text-sm text-gray-900">{{ $application->birth_date->format('F j, Y') }} (Age: {{ $application->age }})</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Email</dt>
                                <dd class="text-sm text-gray-900">
                                    <a href="mailto:{{ $application->email }}" class="text-blue-600 hover:text-blue-800">
                                        {{ $application->email }}
                                    </a>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Mobile Number</dt>
                                <dd class="text-sm text-gray-900">{{ $application->mobile_number }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Education Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4 border-b pb-2">Education Information</h3>
                        <dl class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Educational Qualification</dt>
                                <dd class="text-sm text-gray-900">{{ $application->educational_qualification }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">University</dt>
                                <dd class="text-sm text-gray-900">
                                    {{ $application->university->name ?? $application->university_other ?? 'N/A' }}
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">University Country</dt>
                                <dd class="text-sm text-gray-900">{{ $application->universityCountry->name }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">GPA</dt>
                                <dd class="text-sm text-gray-900">{{ $application->gpa }} / {{ $application->score_from }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Academic Major</dt>
                                <dd class="text-sm text-gray-900">
                                    {{ $application->academic_major === 'Other' ? $application->academic_major_other : $application->academic_major }}
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Graduation Year</dt>
                                <dd class="text-sm text-gray-900">{{ $application->graduation_year }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Professional Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4 border-b pb-2">Professional Information</h3>
                        <dl class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Total Experience</dt>
                                <dd class="text-sm text-gray-900">{{ $application->total_experience }}</dd>
                            </div>
                            @if($application->last_position_title)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Last Position Title</dt>
                                <dd class="text-sm text-gray-900">{{ $application->last_position_title }}</dd>
                            </div>
                            @endif
                            @if($application->last_employer_name)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Last Employer</dt>
                                <dd class="text-sm text-gray-900">{{ $application->last_employer_name }}</dd>
                            </div>
                            @endif
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Lubricants Experience</dt>
                                <dd class="text-sm text-gray-900">
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full {{ $application->has_lubricants_experience ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $application->has_lubricants_experience ? 'Yes' : 'No' }}
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Professional Certificates</dt>
                                <dd class="text-sm text-gray-900">{{ $application->professional_certificates_count }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4 border-b pb-2">Additional Information</h3>
                        <dl class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Languages</dt>
                                <dd class="text-sm text-gray-900">
                                    {{ $application->languages === 'Other' ? $application->languages_other : $application->languages }}
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">How heard about company</dt>
                                <dd class="text-sm text-gray-900">{{ $application->how_heard_about_company }}</dd>
                            </div>
                            @if($application->career_fair_name)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Career Fair</dt>
                                <dd class="text-sm text-gray-900">{{ $application->career_fair_name }} ({{ $application->career_fair_year }})</dd>
                            </div>
                            @endif
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Application Date</dt>
                                <dd class="text-sm text-gray-900">{{ $application->created_at->format('F j, Y g:i A') }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function updateStatus(applicationId, status) {
            fetch(`/admin/applications/${applicationId}/status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({ status: status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to update status');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred');
            });
        }
    </script>
    @endpush
</x-app-layout>
